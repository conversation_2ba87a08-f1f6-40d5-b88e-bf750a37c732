using Simulator.Server.Services;

namespace Simulator.Server.Tests;

/// <summary>
/// 密码服务测试类
/// 注意：这是一个简单的测试示例，生产环境建议使用xUnit或NUnit等测试框架
/// </summary>
public static class PasswordServiceTests
{
    public static void RunTests()
    {
        var passwordService = new PasswordService();
        
        Console.WriteLine("=== 密码服务测试 ===");
        
        // 测试1：基本哈希和验证
        TestBasicHashAndVerify(passwordService);
        
        // 测试2：相同密码生成不同哈希
        TestSamePasswordDifferentHashes(passwordService);
        
        // 测试3：错误密码验证失败
        TestWrongPasswordFails(passwordService);
        
        // 测试4：空密码处理
        TestEmptyPassword(passwordService);
        
        Console.WriteLine("=== 所有测试完成 ===");
    }
    
    private static void TestBasicHashAndVerify(IPasswordService passwordService)
    {
        Console.WriteLine("测试1: 基本哈希和验证");
        
        var password = "TestPassword123!";
        var hash = passwordService.HashPassword(password);
        var isValid = passwordService.VerifyPassword(password, hash);
        
        Console.WriteLine($"原始密码: {password}");
        Console.WriteLine($"哈希长度: {hash.Length} 字符");
        Console.WriteLine($"验证结果: {(isValid ? "✓ 通过" : "✗ 失败")}");
        Console.WriteLine();
    }
    
    private static void TestSamePasswordDifferentHashes(IPasswordService passwordService)
    {
        Console.WriteLine("测试2: 相同密码生成不同哈希（盐值随机性）");
        
        var password = "SamePassword123";
        var hash1 = passwordService.HashPassword(password);
        var hash2 = passwordService.HashPassword(password);
        
        var isDifferent = hash1 != hash2;
        var bothValid = passwordService.VerifyPassword(password, hash1) && 
                       passwordService.VerifyPassword(password, hash2);
        
        Console.WriteLine($"哈希1: {hash1.Substring(0, 20)}...");
        Console.WriteLine($"哈希2: {hash2.Substring(0, 20)}...");
        Console.WriteLine($"哈希不同: {(isDifferent ? "✓ 通过" : "✗ 失败")}");
        Console.WriteLine($"都能验证: {(bothValid ? "✓ 通过" : "✗ 失败")}");
        Console.WriteLine();
    }
    
    private static void TestWrongPasswordFails(IPasswordService passwordService)
    {
        Console.WriteLine("测试3: 错误密码验证失败");
        
        var correctPassword = "CorrectPassword123";
        var wrongPassword = "WrongPassword123";
        var hash = passwordService.HashPassword(correctPassword);
        var isValid = passwordService.VerifyPassword(wrongPassword, hash);
        
        Console.WriteLine($"正确密码: {correctPassword}");
        Console.WriteLine($"错误密码: {wrongPassword}");
        Console.WriteLine($"验证结果: {(!isValid ? "✓ 通过（正确拒绝）" : "✗ 失败（错误接受）")}");
        Console.WriteLine();
    }
    
    private static void TestEmptyPassword(IPasswordService passwordService)
    {
        Console.WriteLine("测试4: 空密码处理");
        
        try
        {
            var emptyHash = passwordService.HashPassword("");
            var isValid = passwordService.VerifyPassword("", emptyHash);
            Console.WriteLine($"空密码哈希: {(emptyHash.Length > 0 ? "✓ 生成成功" : "✗ 生成失败")}");
            Console.WriteLine($"空密码验证: {(isValid ? "✓ 通过" : "✗ 失败")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"空密码处理异常: {ex.Message}");
        }
        Console.WriteLine();
    }
}

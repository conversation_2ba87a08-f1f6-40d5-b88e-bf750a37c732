using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NanoidDotNet;
using System.Security.Claims;
using Simulator.Server.Models;
using Simulator.Server.Models.Dtos;
using Simulator.Server.Services;

namespace Simulator.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UserController(IFreeSql sql, IJwtService jwtService, IPasswordService passwordService, IConfiguration configuration) : Controller
{
    [HttpPost("login")]
    public IActionResult Login([FromBody] LoginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // 根据用户名查找用户
        var user = sql.Select<User>().Where(u => u.Username == request.Username).ToOne();
        if (user == null)
        {
            return Unauthorized(new { message = "用户名或密码错误" });
        }

        // 验证密码
        if (!passwordService.VerifyPassword(request.Password, user.Password))
        {
            return Unauthorized(new { message = "用户名或密码错误" });
        }

        var token = jwtService.GenerateToken(user);
        var expirationMinutes = int.Parse(configuration["Jwt:ExpirationMinutes"] ?? "480");

        var response = new LoginResponse
        {
            Token = token,
            UserId = user.Id,
            Username = user.Username,
            IsTeacher = user.IsTeacher,
            ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes)
        };

        return Ok(response);
    }

    [HttpPost("register")]
    public IActionResult Register([FromBody] RegisterRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // 检查用户名是否已存在
        var existingUser = sql.Select<User>().Where(u => u.Username == request.Username).ToOne();
        if (existingUser != null)
        {
            return BadRequest(new { message = "用户名已存在" });
        }

        // 哈希密码
        var hashedPassword = passwordService.HashPassword(request.Password);

        var user = new User
        {
            Id = Nanoid.Generate(),
            Username = request.Username,
            Password = hashedPassword,
            IsTeacher = request.IsTeacher
        };

        sql.Insert(user).ExecuteAffrows();

        var token = jwtService.GenerateToken(user);
        var expirationMinutes = int.Parse(configuration["Jwt:ExpirationMinutes"] ?? "480");

        var response = new LoginResponse
        {
            Token = token,
            UserId = user.Id,
            Username = user.Username,
            IsTeacher = user.IsTeacher,
            ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes)
        };

        return Ok(response);
    }

    [HttpGet("profile")]
    [Authorize]
    public IActionResult GetProfile()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        var user = sql.Select<User>().Where(u => u.Id == userId).ToOne();
        if (user == null)
        {
            return NotFound(new { message = "用户不存在" });
        }

        return Ok(new
        {
            user.Id,
            user.Username,
            user.IsTeacher
        });
    }
}
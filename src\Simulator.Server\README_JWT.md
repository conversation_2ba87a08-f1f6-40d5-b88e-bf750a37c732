# JWT 认证功能说明

本项目已成功集成JWT（JSON Web Token）认证功能，提供完整的用户认证和授权机制。

## 功能特性

### 1. JWT令牌生成与验证
- 用户登录成功后自动签发JWT令牌
- 令牌包含用户ID、用户名、角色等信息
- 支持令牌过期时间配置（默认8小时）
- 使用HMAC SHA256算法签名

### 2. 用户认证API

#### 用户注册
```http
POST /api/user/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "isTeacher": false
}
```

#### 用户登录
```http
POST /api/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

登录成功返回：
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userId": "user-id",
  "username": "testuser",
  "isTeacher": false,
  "expiresAt": "2025-07-09T18:16:00Z"
}
```

#### 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE
```

### 3. 受保护的API端点

以下API端点需要有效的JWT令牌才能访问：
- `/api/user/profile` - 获取用户信息
- `/api/question/*` - 所有题目相关API

### 4. JWT配置

在 `appsettings.json` 中配置JWT参数：

```json
{
  "Jwt": {
    "SecretKey": "OcuSimulator_JWT_Secret_Key_2025_Very_Long_And_Secure_Key_For_Production_Use",
    "Issuer": "OcuSimulator.Server",
    "Audience": "OcuSimulator.Client",
    "ExpirationMinutes": "480"
  }
}
```

## 使用方法

### 1. 客户端认证流程
1. 用户注册或登录获取JWT令牌
2. 在后续API请求的Header中添加：`Authorization: Bearer <token>`
3. 服务器自动验证令牌有效性

### 2. 令牌验证
- 服务器自动验证令牌签名
- 检查令牌是否过期
- 验证发行者和受众
- 提取用户信息供控制器使用

### 3. 错误处理
- 无效令牌：返回401 Unauthorized
- 令牌过期：返回401 Unauthorized
- 缺少令牌：返回401 Unauthorized

## 密码安全实现

### 密码哈希策略
本项目采用**服务端哈希**的最佳实践：

1. **注册流程**：
   - 客户端以明文形式发送密码（通过HTTPS加密传输）
   - 服务端接收后立即使用PBKDF2算法进行哈希
   - 存储哈希值到数据库，原始密码不保留

2. **登录流程**：
   - 客户端发送明文密码
   - 服务端根据用户名查找用户
   - 使用相同算法验证密码与存储的哈希值

3. **哈希算法**：
   - 使用PBKDF2 + SHA256
   - 随机生成16字节盐值
   - 10,000次迭代
   - 32字节密钥长度

### 为什么选择服务端哈希？

**优势**：
- 🔒 **安全性更高**：服务端可以使用更强的哈希算法和参数
- 🔄 **可升级性**：可以随时升级哈希算法而不影响客户端
- 🛡️ **防止彩虹表攻击**：每个密码使用唯一的随机盐值
- 🌐 **兼容性好**：不依赖客户端实现，支持各种客户端

**传输安全**：
- 所有通信通过HTTPS加密
- 密码在服务端内存中只存在极短时间
- 立即进行哈希处理，不记录日志

## 安全注意事项

1. **密钥安全**：生产环境中应使用更强的JWT密钥，并通过环境变量配置
2. **HTTPS强制**：生产环境必须使用HTTPS传输所有敏感数据
3. **令牌存储**：客户端应安全存储令牌，避免XSS攻击
4. **密码策略**：建议实施密码复杂度要求和定期更换策略
5. **速率限制**：建议对登录API实施速率限制防止暴力破解

## 测试

使用 `Simulator.Server.http` 文件中的示例请求测试JWT功能：

1. 先调用注册或登录API获取令牌
2. 复制返回的token值
3. 在需要认证的API请求中替换 `YOUR_JWT_TOKEN_HERE`
4. 发送请求验证功能

## 技术实现

- **JWT库**：Microsoft.AspNetCore.Authentication.JwtBearer
- **签名算法**：HMAC SHA256
- **令牌格式**：标准JWT格式
- **Claims**：包含用户ID、用户名、角色等信息
